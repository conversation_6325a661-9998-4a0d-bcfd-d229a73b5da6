using System.Collections;
using System.Collections.Generic;
using Fantasy;
using Fantasy.Async;
using Fantasy.Network;
using UnityEngine;
using UnityEngine.UI;

public class TestView : MonoBehaviour
{
    private Button _connectButton;
    private Button _sendNormalButton;
    private Button _sendRpcButton;
    private Button _sendNormalRouteButton;
    private Button _sendRpcRouteButton;
    
    private Fantasy.Scene _fantasyScene;
    private Fantasy.Network.Session _fantasySession;
    void Start()
    {
        _connectButton = transform.Find("ConnectButton").GetComponent<Button>();
        _connectButton.onClick.AddListener(() =>
        {
            _fantasySession = _fantasyScene.Connect("127.0.0.1:20000", NetworkProtocolType.KCP, () =>
            {
                Debug.Log("连接成功");
                _fantasySession.AddComponent<SessionHeartbeatComponent>().Start(2000);
            }, () =>
            {
                Debug.Log("连接失败");
                _fantasySession?.Dispose();
                _fantasySession = null;
            }, () =>
            {
                Debug.Log("连接断开");
                _fantasySession?.Dispose();
                _fantasySession = null;
            },false);
        });
        _sendNormalButton = transform.Find("SendNormalButton").GetComponent<Button>();
        _sendNormalButton.onClick.AddListener(() =>
        {
            _fantasySession.Send(new C2G_HelloMessage(){Tag = "Message, hello gate!!"});
        });
        _sendRpcButton = transform.Find("SendRpcButton").GetComponent<Button>();
        _sendRpcButton.onClick.AddListener(() =>
        {
            SendTestRequest().Coroutine();
        });
        
        _sendNormalRouteButton = transform.Find("SendNormalRouteButton").GetComponent<Button>();
        _sendNormalRouteButton.onClick.AddListener(() =>
        {
            _fantasySession.Send(new C2Chat_HelloMessage(){Tag = "Message, hello chat!"});
        });
        _sendRpcRouteButton = transform.Find("SendRpcRouteButton").GetComponent<Button>();
        _sendRpcRouteButton.onClick.AddListener(() => { });
        
        Init().Coroutine();
    }
    
    void OnDestroy()
    {
        _fantasySession?.Dispose();
        _fantasySession = null;
        _fantasyScene?.Dispose();
        _fantasyScene = null;
    }

    private async FTask Init()
    {
        await Fantasy.Platform.Unity.Entry.Initialize(GetType().Assembly);
        _fantasyScene?.Dispose();
        _fantasyScene = await Fantasy.Platform.Unity.Entry.CreateScene();
        Debug.Log("Fantasy Scene Created");
    }

    private async FTask SendTestRequest()
    {
        var response = await _fantasySession.Call(new C2G_HelloRequest(){Tag = "Request, hello gate!"});
        if (response is G2C_HelloResponse rep)
        {
            Debug.Log($"RPC Response: {rep.Tag}");
        }
        await FTask.CompletedTask;
    }
}
